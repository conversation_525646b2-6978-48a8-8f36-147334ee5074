<script lang="ts">
  import { onMount } from 'svelte';
  import { appApi } from '$lib/api.js';
  import { appState, setLoading, addError } from '$lib/stores/app.svelte.js';
  import '../app.css';

  onMount(async () => {
    try {
      setLoading(true, 'Initializing application...');
      
      // Initialize the Tauri backend
      await appApi.initializeApp();
      
      // Load app info
      const appInfo = await appApi.getAppInfo();
      console.log('App initialized:', appInfo);
      
      appState.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize app:', error);
      addError({
        type: 'unknown',
        message: `Failed to initialize application: ${error}`,
        timestamp: new Date()
      });
    } finally {
      setLoading(false);
    }
  });
</script>

<div class="app">
  <slot />
</div>

<style>
  .app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-color);
    color: var(--text-color);
  }

  :global(:root) {
    --bg-color: #ffffff;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --primary-color: #007acc;
    --secondary-color: #f5f5f5;
  }

  :global([data-theme="dark"]) {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
    --primary-color: #4a9eff;
    --secondary-color: #2a2a2a;
  }

  :global(body) {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
</style>