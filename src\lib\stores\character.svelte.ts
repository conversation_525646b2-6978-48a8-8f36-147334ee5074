// Character state management using Svelte 5 runes
import type { Character } from '../types.js';

// Character state
export const characterState = $state({
  characters: [] as Character[],
  currentCharacter: null as Character | null,
  isEditing: false,
  editingCharacter: null as Character | null
});

// Character management state
export const characterManagementState = $state({
  isLoading: false,
  searchQuery: '',
  filteredCharacters: [] as Character[],
  selectedTags: [] as string[],
  availableTags: [] as string[]
});

// Helper functions
export function setCurrentCharacter(character: Character | null) {
  characterState.currentCharacter = character;
}

export function addCharacter(character: Character) {
  characterState.characters.push(character);
  updateAvailableTags();
  updateFilteredCharacters();
}

export function updateCharacter(updatedCharacter: Character) {
  const index = characterState.characters.findIndex(c => c.id === updatedCharacter.id);
  if (index !== -1) {
    characterState.characters[index] = updatedCharacter;
    updateAvailableTags();
    updateFilteredCharacters();
  }
}

export function removeCharacter(characterId: string) {
  characterState.characters = characterState.characters.filter(c => c.id !== characterId);
  if (characterState.currentCharacter?.id === characterId) {
    characterState.currentCharacter = null;
  }
  updateAvailableTags();
  updateFilteredCharacters();
}

export function startEditingCharacter(character: Character | null = null) {
  characterState.isEditing = true;
  characterState.editingCharacter = character ? { ...character } : createEmptyCharacter();
}

export function stopEditingCharacter() {
  characterState.isEditing = false;
  characterState.editingCharacter = null;
}

export function updateFilteredCharacters() {
  const query = characterManagementState.searchQuery.toLowerCase();
  const selectedTags = characterManagementState.selectedTags;
  
  let filtered = characterState.characters;
  
  if (query) {
    filtered = filtered.filter(character =>
      character.name.toLowerCase().includes(query) ||
      character.description.toLowerCase().includes(query)
    );
  }
  
  if (selectedTags.length > 0) {
    filtered = filtered.filter(character =>
      selectedTags.some(tag => character.tags.includes(tag))
    );
  }
  
  characterManagementState.filteredCharacters = filtered;
}

export function updateAvailableTags() {
  const allTags = new Set<string>();
  characterState.characters.forEach(character => {
    character.tags.forEach(tag => allTags.add(tag));
  });
  characterManagementState.availableTags = Array.from(allTags).sort();
}

function createEmptyCharacter(): Character {
  return {
    id: '',
    name: '',
    description: '',
    personality: '',
    scenario: '',
    first_message: '',
    example_dialogs: [],
    tags: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}