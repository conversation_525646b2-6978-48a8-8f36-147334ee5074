// Core application types

export interface ChatMessage {
  id: string;
  chat_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface Character {
  id: string;
  name: string;
  description: string;
  personality: string;
  scenario: string;
  first_message: string;
  example_dialogs: string[];
  avatar_path?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface AIProviderConfig {
  provider_type: AIProviderType;
  api_key?: string;
  base_url?: string;
  model: string;
  parameters: AIParameters;
}

export interface AIParameters {
  temperature: number;
  top_p: number;
  top_k?: number;
  max_tokens?: number;
  repetition_penalty?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

export type AIProviderType = 
  | 'openai'
  | 'claude'
  | 'google'
  | 'azure'
  | 'novelai'
  | 'koboldai'
  | 'textgen_webui';

export interface Chat {
  id: string;
  title: string;
  character_id?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  default_ai_provider?: string;
  auto_save: boolean;
  [key: string]: any;
}

export interface AppError {
  type: 'database' | 'io' | 'serialization' | 'ai_provider' | 'plugin' | 'authentication' | 'validation' | 'unknown';
  message: string;
  timestamp: Date;
}