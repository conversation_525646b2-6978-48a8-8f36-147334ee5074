// Global application state using Svelte 5 runes
import type { AppSettings, AppError } from '../types.js';

// Application state
export const appState = $state({
  isInitialized: false,
  currentChat: null as string | null,
  selectedCharacter: null as string | null,
  aiProviders: [] as any[],
  settings: {
    theme: 'auto',
    language: 'en',
    auto_save: true
  } as AppSettings,
  plugins: [] as any[]
});

// Error state
export const errorState = $state({
  errors: [] as AppError[],
  isShowingError: false
});

// Loading state
export const loadingState = $state({
  isLoading: false,
  loadingMessage: ''
});

// UI state
export const uiState = $state({
  sidebarOpen: true,
  settingsOpen: false,
  characterEditorOpen: false
});

// Helper functions
export function addError(error: AppError) {
  errorState.errors.push(error);
  errorState.isShowingError = true;
}

export function clearErrors() {
  errorState.errors = [];
  errorState.isShowingError = false;
}

export function setLoading(loading: boolean, message = '') {
  loadingState.isLoading = loading;
  loadingState.loadingMessage = message;
}