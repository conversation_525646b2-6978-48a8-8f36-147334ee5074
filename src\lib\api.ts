// Tauri API client
import { invoke } from '@tauri-apps/api/core';
import type { ChatMessage, Character, Chat, AIProviderConfig } from './types.js';

// Chat API
export const chatApi = {
  async sendMessage(
    message: string,
    characterId?: string,
    providerConfig?: AIProviderConfig
  ): Promise<string> {
    return await invoke('send_message', {
      message,
      characterId,
      providerConfig
    });
  },

  async getChatHistory(chatId: string): Promise<ChatMessage[]> {
    return await invoke('get_chat_history', { chatId });
  },

  async createChat(title: string, characterId?: string): Promise<Chat> {
    return await invoke('create_chat', { title, characterId });
  },

  async deleteChat(chatId: string): Promise<void> {
    return await invoke('delete_chat', { chatId });
  },

  async getAllChats(): Promise<Chat[]> {
    return await invoke('get_all_chats');
  }
};

// Character API
export const characterApi = {
  async createCharacter(character: Omit<Character, 'id' | 'created_at' | 'updated_at'>): Promise<Character> {
    return await invoke('create_character', { character });
  },

  async updateCharacter(character: Character): Promise<Character> {
    return await invoke('update_character', { character });
  },

  async deleteCharacter(characterId: string): Promise<void> {
    return await invoke('delete_character', { characterId });
  },

  async getAllCharacters(): Promise<Character[]> {
    return await invoke('get_all_characters');
  },

  async importCharacter(filePath: string, format: string): Promise<Character> {
    return await invoke('import_character', { filePath, format });
  },

  async exportCharacter(characterId: string, filePath: string, format: string): Promise<void> {
    return await invoke('export_character', { characterId, filePath, format });
  }
};

// AI Provider API
export const aiProviderApi = {
  async configureProvider(provider: string, config: any): Promise<void> {
    return await invoke('configure_ai_provider', { provider, config });
  },

  async testConnection(provider: string): Promise<boolean> {
    return await invoke('test_ai_connection', { provider });
  },

  async getProviders(): Promise<any[]> {
    return await invoke('get_ai_providers');
  }
};

// Settings API
export const settingsApi = {
  async getSetting(key: string): Promise<any> {
    return await invoke('get_setting', { key });
  },

  async setSetting(key: string, value: any): Promise<void> {
    return await invoke('set_setting', { key, value });
  },

  async getAllSettings(): Promise<Record<string, any>> {
    return await invoke('get_all_settings');
  }
};

// App API
export const appApi = {
  async initializeApp(): Promise<void> {
    return await invoke('initialize_app');
  },

  async getAppInfo(): Promise<any> {
    return await invoke('get_app_info');
  }
};