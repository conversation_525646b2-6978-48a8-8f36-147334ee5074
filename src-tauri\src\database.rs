use sqlx::{migrate::MigrateDatabase, Sqlite, SqlitePool};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use std::fs;

pub type DbPool = SqlitePool;

pub async fn init_database(app_handle: &AppHandle) -> Result<DbPool, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .ok_or("Failed to get app data directory")?;

    if !app_data_dir.exists() {
        fs::create_dir_all(&app_data_dir)?;
    }
    
    let db_path = app_data_dir.join("database.sqlite");
    let db_url = format!("sqlite:{}", db_path.to_str().ok_or("Failed to convert path to string")?);

    if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {
        Sqlite::create_database(&db_url).await?;
    }

    let pool = SqlitePool::connect(&db_url).await?;
    
    sqlx::migrate!("./migrations").run(&pool).await?;

    Ok(pool)
}
