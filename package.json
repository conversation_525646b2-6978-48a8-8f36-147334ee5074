{"name": "tavern-re", "version": "0.1.0", "description": "A modern AI chat client built with Tauri + SvelteKit", "type": "module", "packageManager": "pnpm@9.0.0", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "license": "MIT", "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-dialog": "^2"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "~5.6.2", "vite": "^6.0.3", "@tauri-apps/cli": "^2", "@types/node": "^22.0.0"}}