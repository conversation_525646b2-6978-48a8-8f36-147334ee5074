// Chat state management using Svelte 5 runes
import type { ChatMessage, Chat } from '../types.js';

// Chat state
export const chatState = $state({
  messages: [] as ChatMessage[],
  currentChat: null as Chat | null,
  isStreaming: false,
  currentProvider: null as string | null,
  streamingMessage: ''
});

// Chat history state
export const chatHistoryState = $state({
  chats: [] as Chat[],
  isLoading: false,
  searchQuery: '',
  filteredChats: [] as Chat[]
});

// Helper functions
export function addMessage(message: ChatMessage) {
  chatState.messages.push(message);
}

export function updateStreamingMessage(content: string) {
  chatState.streamingMessage = content;
}

export function setStreaming(streaming: boolean) {
  chatState.isStreaming = streaming;
  if (!streaming) {
    chatState.streamingMessage = '';
  }
}

export function setCurrentChat(chat: Chat | null) {
  chatState.currentChat = chat;
  if (chat) {
    // Load messages for this chat
    loadChatMessages(chat.id);
  } else {
    chatState.messages = [];
  }
}

export function addChat(chat: Chat) {
  chatHistoryState.chats.unshift(chat);
  updateFilteredChats();
}

export function updateFilteredChats() {
  const query = chatHistoryState.searchQuery.toLowerCase();
  if (!query) {
    chatHistoryState.filteredChats = chatHistoryState.chats;
  } else {
    chatHistoryState.filteredChats = chatHistoryState.chats.filter(chat =>
      chat.title.toLowerCase().includes(query)
    );
  }
}

// Placeholder for loading chat messages
async function loadChatMessages(chatId: string) {
  // This will be implemented when we add Tauri commands
  console.log('Loading messages for chat:', chatId);
}