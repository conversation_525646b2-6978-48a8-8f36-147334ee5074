<script lang="ts">
  import { onMount } from "svelte";
  import { invoke } from "@tauri-apps/api/core";
  
  let greetMsg = $state("");
  let name = $state("");

  async function greet() {
    greetMsg = await invoke("greet", { name });
  }

  onMount(() => {
    console.log("Tavern RE initialized");
  });
</script>

<main class="container">
  <h1>Welcome to Tavern RE</h1>

  <div class="row">
    <a href="https://vitejs.dev" target="_blank">
      <img src="/vite.svg" class="logo vite" alt="Vite Logo" />
    </a>
    <a href="https://tauri.app" target="_blank">
      <img src="/tauri.svg" class="logo tauri" alt="Tauri Logo" />
    </a>
    <a href="https://svelte.dev" target="_blank">
      <img src="/svelte.svg" class="logo svelte" alt="Svelte Logo" />
    </a>
  </div>

  <p>Click on the Tauri, Vite, and Svelte logos to learn more.</p>

  <form class="row" on:submit|preventDefault={greet}>
    <input
      id="greet-input"
      placeholder="Enter a name..."
      bind:value={name}
    />
    <button type="submit">Greet</button>
  </form>

  <p>{greetMsg}</p>
</main>

<style>
  .logo.vite:hover {
    filter: drop-shadow(0 0 2em #747bff);
  }

  .logo.svelte:hover {
    filter: drop-shadow(0 0 2em #ff3e00);
  }

  .logo.tauri:hover {
    filter: drop-shadow(0 0 2em #24c8db);
  }
</style>