# Design Document

## Overview

本设计文档详细描述了基于 Tauri + Svelte 技术栈的现代化 AI 聊天客户端架构。该应用将提供高性能、安全的桌面体验，同时保持与现有 SillyTavern 生态系统的兼容性。

### 核心技术选择

- **后端**: Rust + Tauri - 提供原生性能、内存安全和系统集成
- **前端**: Svelte 5 - 编译时优化、小体积、优秀的响应式系统
- **数据库**: SQLite - 嵌入式、高性能、ACID 事务支持
- **IPC**: Tauri Commands - 类型安全的前后端通信
- **状态管理**: Svelte Runes - 现代响应式状态管理

## Architecture

### 整体架构图

```mermaid
graph TB
    subgraph "Frontend (Svelte)"
        UI[UI Components]
        Store[State Management]
        API[API Client]
    end
    
    subgraph "Tauri IPC Layer"
        Commands[Tauri Commands]
        Events[Event System]
    end
    
    subgraph "Backend (Rust)"
        Core[Core Services]
        DB[Database Layer]
        AI[AI Integrations]
        Plugin[Plugin System]
        Security[Security Layer]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Claude[Claude API]
        Local[Local Models]
    end
    
    UI --> Store
    Store --> API
    API --> Commands
    Commands --> Core
    Core --> DB
    Core --> AI
    Core --> Plugin
    AI --> OpenAI
    AI --> Claude
    AI --> Local
    
    Events --> Store
    Security --> Plugin
```

### 分层架构

1. **表现层 (Presentation Layer)** - Svelte 组件和 UI 逻辑
2. **应用层 (Application Layer)** - 业务逻辑和状态管理
3. **服务层 (Service Layer)** - Tauri Commands 和核心服务
4. **数据层 (Data Layer)** - SQLite 数据库和文件系统
5. **集成层 (Integration Layer)** - 外部 API 和插件系统

## Components and Interfaces

### 前端组件架构

```typescript
// 核心状态管理 (state.svelte.ts)
export const appState = $state({
  currentChat: null,
  selectedCharacter: null,
  aiProviders: [],
  settings: {},
  plugins: []
});

// 聊天状态管理
export const chatState = $state({
  messages: [],
  isStreaming: false,
  currentProvider: null
});

// 角色管理状态
export const characterState = $state({
  characters: [],
  currentCharacter: null,
  isEditing: false
});
```

### 主要 Svelte 组件

```
src/
├── components/
│   ├── chat/
│   │   ├── ChatWindow.svelte
│   │   ├── MessageList.svelte
│   │   ├── MessageInput.svelte
│   │   └── StreamingIndicator.svelte
│   ├── character/
│   │   ├── CharacterCard.svelte
│   │   ├── CharacterEditor.svelte
│   │   └── CharacterImporter.svelte
│   ├── settings/
│   │   ├── SettingsPanel.svelte
│   │   ├── AIProviderConfig.svelte
│   │   └── PluginManager.svelte
│   └── common/
│       ├── Modal.svelte
│       ├── FileDropZone.svelte
│       └── ThemeProvider.svelte
├── stores/
│   ├── app.svelte.ts
│   ├── chat.svelte.ts
│   └── character.svelte.ts
└── utils/
    ├── api.ts
    ├── types.ts
    └── constants.ts
```

### Tauri Commands 接口

```rust
// 聊天相关命令
#[tauri::command]
async fn send_message(
    message: String,
    character_id: Option<String>,
    provider_config: AIProviderConfig,
    state: State<'_, AppState>
) -> Result<String, String>;

#[tauri::command]
async fn get_chat_history(
    chat_id: String,
    state: State<'_, AppState>
) -> Result<Vec<ChatMessage>, String>;

// 角色管理命令
#[tauri::command]
async fn create_character(
    character: CharacterData,
    state: State<'_, AppState>
) -> Result<String, String>;

#[tauri::command]
async fn import_character(
    file_path: String,
    format: CharacterFormat,
    state: State<'_, AppState>
) -> Result<Character, String>;

// AI 提供商命令
#[tauri::command]
async fn configure_ai_provider(
    provider: AIProvider,
    config: ProviderConfig,
    state: State<'_, AppState>
) -> Result<(), String>;

#[tauri::command]
async fn test_ai_connection(
    provider: AIProvider,
    state: State<'_, AppState>
) -> Result<bool, String>;

// 插件系统命令
#[tauri::command]
async fn install_plugin(
    plugin_path: String,
    state: State<'_, AppState>
) -> Result<PluginInfo, String>;

#[tauri::command]
async fn execute_plugin_command(
    plugin_id: String,
    command: String,
    args: serde_json::Value,
    state: State<'_, AppState>
) -> Result<serde_json::Value, String>;
```

### 核心数据结构

```rust
// 聊天消息结构
#[derive(Serialize, Deserialize, Clone)]
pub struct ChatMessage {
    pub id: String,
    pub chat_id: String,
    pub role: MessageRole,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

// 角色数据结构
#[derive(Serialize, Deserialize, Clone)]
pub struct Character {
    pub id: String,
    pub name: String,
    pub description: String,
    pub personality: String,
    pub scenario: String,
    pub first_message: String,
    pub example_dialogs: Vec<String>,
    pub avatar_path: Option<String>,
    pub tags: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// AI 提供商配置
#[derive(Serialize, Deserialize, Clone)]
pub struct AIProviderConfig {
    pub provider_type: AIProviderType,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub model: String,
    pub parameters: AIParameters,
}

// AI 参数
#[derive(Serialize, Deserialize, Clone)]
pub struct AIParameters {
    pub temperature: f32,
    pub top_p: f32,
    pub top_k: Option<i32>,
    pub max_tokens: Option<i32>,
    pub repetition_penalty: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
}
```

## Data Models

### 数据库架构

```sql
-- 聊天表
CREATE TABLE chats (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    character_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT, -- JSON
    FOREIGN KEY (character_id) REFERENCES characters(id)
);

-- 消息表
CREATE TABLE messages (
    id TEXT PRIMARY KEY,
    chat_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT, -- JSON
    FOREIGN KEY (chat_id) REFERENCES chats(id) ON DELETE CASCADE
);

-- 角色表
CREATE TABLE characters (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    personality TEXT,
    scenario TEXT,
    first_message TEXT,
    example_dialogs TEXT, -- JSON array
    avatar_path TEXT,
    tags TEXT, -- JSON array
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI 提供商配置表
CREATE TABLE ai_providers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider_type TEXT NOT NULL,
    config TEXT NOT NULL, -- JSON (encrypted)
    is_active BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 世界信息表
CREATE TABLE world_info (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    keywords TEXT NOT NULL, -- JSON array
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    character_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (character_id) REFERENCES characters(id)
);

-- 插件表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    version TEXT NOT NULL,
    description TEXT,
    author TEXT,
    is_enabled BOOLEAN DEFAULT TRUE,
    config TEXT, -- JSON
    install_path TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户设置表
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL, -- JSON
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 文件系统结构

```
用户数据目录/
├── chats/
│   ├── {chat_id}.jsonl          # 聊天历史文件
│   └── backups/
│       └── {chat_id}_{timestamp}.jsonl
├── characters/
│   ├── {character_id}.json      # 角色数据
│   └── avatars/
│       └── {character_id}.{ext} # 角色头像
├── plugins/
│   ├── {plugin_id}/
│   │   ├── manifest.json        # 插件清单
│   │   ├── main.js             # 插件主文件
│   │   └── assets/             # 插件资源
├── backgrounds/
│   └── {background_id}.{ext}    # 背景图片
├── settings/
│   ├── app.json                # 应用设置
│   ├── themes.json             # 主题配置
│   └── keybindings.json        # 快捷键配置
└── logs/
    ├── app.log                 # 应用日志
    └── error.log               # 错误日志
```

## Error Handling

### 错误处理策略

```rust
// 自定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("AI provider error: {0}")]
    AIProvider(String),
    
    #[error("Plugin error: {0}")]
    Plugin(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
}

// 错误处理中间件
impl From<AppError> for String {
    fn from(error: AppError) -> Self {
        error.to_string()
    }
}
```

### 前端错误处理

```typescript
// 错误状态管理
export const errorState = $state({
  errors: [] as AppError[],
  isShowingError: false
});

// 错误处理工具
export class ErrorHandler {
  static handle(error: unknown) {
    const appError = this.parseError(error);
    errorState.errors.push(appError);
    this.logError(appError);
    this.showNotification(appError);
  }
  
  static parseError(error: unknown): AppError {
    if (error instanceof Error) {
      return {
        type: 'unknown',
        message: error.message,
        timestamp: new Date()
      };
    }
    return {
      type: 'unknown',
      message: String(error),
      timestamp: new Date()
    };
  }
}
```

## Testing Strategy

### 测试架构

```
tests/
├── unit/
│   ├── rust/
│   │   ├── services/
│   │   ├── database/
│   │   └── plugins/
│   └── svelte/
│       ├── components/
│       ├── stores/
│       └── utils/
├── integration/
│   ├── api_tests.rs
│   ├── database_tests.rs
│   └── plugin_tests.rs
├── e2e/
│   ├── chat_flow.spec.ts
│   ├── character_management.spec.ts
│   └── plugin_system.spec.ts
└── fixtures/
    ├── characters/
    ├── chats/
    └── plugins/
```

### Rust 测试示例

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_send_message() {
        let app_state = create_test_state().await;
        let message = "Hello, world!".to_string();
        let character_id = Some("test_character".to_string());
        let provider_config = create_test_provider_config();
        
        let result = send_message(
            message,
            character_id,
            provider_config,
            State::from(app_state)
        ).await;
        
        assert!(result.is_ok());
    }
    
    #[tokio::test]
    async fn test_character_import() {
        let app_state = create_test_state().await;
        let file_path = "tests/fixtures/characters/test_character.json".to_string();
        let format = CharacterFormat::Json;
        
        let result = import_character(
            file_path,
            format,
            State::from(app_state)
        ).await;
        
        assert!(result.is_ok());
        let character = result.unwrap();
        assert_eq!(character.name, "Test Character");
    }
}
```

### Svelte 测试示例

```typescript
// ChatWindow.test.ts
import { render, fireEvent } from '@testing-library/svelte';
import { expect, test } from 'vitest';
import ChatWindow from '../components/chat/ChatWindow.svelte';

test('should send message when enter is pressed', async () => {
  const { getByTestId } = render(ChatWindow);
  const input = getByTestId('message-input');
  
  await fireEvent.input(input, { target: { value: 'Hello' } });
  await fireEvent.keyDown(input, { key: 'Enter' });
  
  expect(getByTestId('message-list')).toContainHTML('Hello');
});

test('should display streaming indicator when AI is responding', async () => {
  const { getByTestId } = render(ChatWindow);
  
  // Simulate streaming state
  chatState.isStreaming = true;
  
  expect(getByTestId('streaming-indicator')).toBeInTheDocument();
});
```

## 核心服务实现

### AI 集成服务

```rust
pub struct AIService {
    providers: HashMap<String, Box<dyn AIProvider>>,
    default_provider: Option<String>,
}

impl AIService {
    pub async fn send_message(
        &self,
        message: &str,
        context: &ChatContext,
        config: &AIProviderConfig,
    ) -> Result<AIResponse, AppError> {
        let provider = self.get_provider(&config.provider_type)?;
        
        let prompt = self.build_prompt(message, context, config).await?;
        let response = provider.generate(prompt, &config.parameters).await?;
        
        Ok(response)
    }
    
    pub async fn stream_message(
        &self,
        message: &str,
        context: &ChatContext,
        config: &AIProviderConfig,
        callback: impl Fn(String) + Send + 'static,
    ) -> Result<(), AppError> {
        let provider = self.get_provider(&config.provider_type)?;
        
        let prompt = self.build_prompt(message, context, config).await?;
        provider.stream_generate(prompt, &config.parameters, callback).await?;
        
        Ok(())
    }
}

// AI 提供商特征
#[async_trait]
pub trait AIProvider: Send + Sync {
    async fn generate(
        &self,
        prompt: String,
        parameters: &AIParameters,
    ) -> Result<AIResponse, AppError>;
    
    async fn stream_generate(
        &self,
        prompt: String,
        parameters: &AIParameters,
        callback: impl Fn(String) + Send + 'static,
    ) -> Result<(), AppError>;
    
    fn validate_config(&self, config: &ProviderConfig) -> Result<(), AppError>;
}
```

### 插件系统架构

```rust
pub struct PluginSystem {
    runtime: PluginRuntime,
    plugins: HashMap<String, PluginInstance>,
    security_manager: SecurityManager,
}

impl PluginSystem {
    pub async fn install_plugin(&mut self, plugin_path: &str) -> Result<PluginInfo, AppError> {
        // 1. 验证插件包
        let manifest = self.validate_plugin_package(plugin_path)?;
        
        // 2. 安全检查
        self.security_manager.scan_plugin(&manifest)?;
        
        // 3. 创建沙箱环境
        let sandbox = self.create_sandbox(&manifest)?;
        
        // 4. 加载插件
        let instance = self.runtime.load_plugin(plugin_path, sandbox).await?;
        
        // 5. 注册插件
        self.plugins.insert(manifest.id.clone(), instance);
        
        Ok(PluginInfo::from(manifest))
    }
    
    pub async fn execute_plugin_command(
        &self,
        plugin_id: &str,
        command: &str,
        args: serde_json::Value,
    ) -> Result<serde_json::Value, AppError> {
        let plugin = self.plugins.get(plugin_id)
            .ok_or_else(|| AppError::Plugin("Plugin not found".to_string()))?;
        
        // 权限检查
        self.security_manager.check_permission(plugin_id, command)?;
        
        // 执行命令
        plugin.execute_command(command, args).await
    }
}

// 插件沙箱
pub struct PluginSandbox {
    allowed_apis: HashSet<String>,
    resource_limits: ResourceLimits,
    file_access: FileAccessPolicy,
}

// 安全管理器
pub struct SecurityManager {
    policies: Vec<SecurityPolicy>,
    threat_detector: ThreatDetector,
}
```

### 数据库服务

```rust
pub struct DatabaseService {
    pool: SqlitePool,
    encryption_key: Option<Vec<u8>>,
}

impl DatabaseService {
    pub async fn new(database_url: &str) -> Result<Self, AppError> {
        let pool = SqlitePool::connect(database_url).await?;
        
        // 运行迁移
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        Ok(Self {
            pool,
            encryption_key: None,
        })
    }
    
    pub async fn save_message(&self, message: &ChatMessage) -> Result<(), AppError> {
        let metadata_json = serde_json::to_string(&message.metadata)?;
        
        sqlx::query!(
            "INSERT INTO messages (id, chat_id, role, content, timestamp, metadata) 
             VALUES (?, ?, ?, ?, ?, ?)",
            message.id,
            message.chat_id,
            message.role.to_string(),
            message.content,
            message.timestamp,
            metadata_json
        )
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn get_chat_history(&self, chat_id: &str, limit: i32) -> Result<Vec<ChatMessage>, AppError> {
        let rows = sqlx::query!(
            "SELECT * FROM messages WHERE chat_id = ? ORDER BY timestamp DESC LIMIT ?",
            chat_id,
            limit
        )
        .fetch_all(&self.pool)
        .await?;
        
        let messages = rows.into_iter()
            .map(|row| ChatMessage {
                id: row.id,
                chat_id: row.chat_id,
                role: MessageRole::from_str(&row.role).unwrap(),
                content: row.content,
                timestamp: row.timestamp,
                metadata: row.metadata.and_then(|m| serde_json::from_str(&m).ok()),
            })
            .collect();
        
        Ok(messages)
    }
}
```

## 安全特性

### 数据加密

```rust
pub struct EncryptionService {
    cipher: ChaCha20Poly1305,
    key_derivation: Argon2,
}

impl EncryptionService {
    pub fn encrypt_sensitive_data(&self, data: &str) -> Result<String, AppError> {
        let nonce = ChaCha20Poly1305::generate_nonce(&mut OsRng);
        let ciphertext = self.cipher.encrypt(&nonce, data.as_bytes())?;
        
        let encrypted_data = EncryptedData {
            nonce: nonce.to_vec(),
            ciphertext,
        };
        
        Ok(base64::encode(serde_json::to_vec(&encrypted_data)?))
    }
    
    pub fn decrypt_sensitive_data(&self, encrypted: &str) -> Result<String, AppError> {
        let data: EncryptedData = serde_json::from_slice(&base64::decode(encrypted)?)?;
        let nonce = GenericArray::from_slice(&data.nonce);
        
        let plaintext = self.cipher.decrypt(nonce, data.ciphertext.as_ref())?;
        Ok(String::from_utf8(plaintext)?)
    }
}
```

### 输入验证和净化

```rust
pub struct InputValidator;

impl InputValidator {
    pub fn validate_message(content: &str) -> Result<String, AppError> {
        // 长度检查
        if content.len() > MAX_MESSAGE_LENGTH {
            return Err(AppError::Validation("Message too long".to_string()));
        }
        
        // HTML 净化
        let sanitized = ammonia::clean(content);
        
        // XSS 防护
        if Self::contains_suspicious_patterns(&sanitized) {
            return Err(AppError::Validation("Suspicious content detected".to_string()));
        }
        
        Ok(sanitized)
    }
    
    pub fn validate_character_data(character: &Character) -> Result<(), AppError> {
        if character.name.trim().is_empty() {
            return Err(AppError::Validation("Character name cannot be empty".to_string()));
        }
        
        if character.name.len() > MAX_CHARACTER_NAME_LENGTH {
            return Err(AppError::Validation("Character name too long".to_string()));
        }
        
        Ok(())
    }
}
```

这个设计文档提供了完整的技术架构，涵盖了所有核心功能的实现方案。架构采用了现代化的技术栈，确保了性能、安全性和可维护性。插件系统通过沙箱机制保证了安全性，同时保持了扩展性。数据库设计支持复杂的查询需求，文件系统结构清晰易于管理。