mod database;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{State, Manager};
use tokio::sync::Mutex;

// Core data structures
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub chat_id: String,
    pub role: String,
    pub content: String,
    pub timestamp: String,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Character {
    pub id: String,
    pub name: String,
    pub description: String,
    pub personality: String,
    pub scenario: String,
    pub first_message: String,
    pub example_dialogs: Vec<String>,
    pub avatar_path: Option<String>,
    pub tags: Vec<String>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Chat {
    pub id: String,
    pub title: String,
    pub character_id: Option<String>,
    pub created_at: String,
    pub updated_at: String,
    pub metadata: Option<serde_json::Value>,
}

// Application state
#[derive(Debug)]
pub struct AppState {
    db_pool: database::DbPool,
    pub chats: Mutex<HashMap<String, Chat>>,
    pub characters: Mutex<HashMap<String, Character>>,
    pub settings: Mutex<HashMap<String, serde_json::Value>>,
}

// Tauri commands
#[tauri::command]
async fn initialize_app(state: State<'_, AppState>) -> Result<(), String> {
    println!("Initializing Tavern RE application...");
    
    // Initialize default settings
    let mut settings = state.settings.lock().await;
    settings.insert("theme".to_string(), serde_json::Value::String("auto".to_string()));
    settings.insert("language".to_string(), serde_json::Value::String("en".to_string()));
    settings.insert("auto_save".to_string(), serde_json::Value::Bool(true));
    
    Ok(())
}

#[tauri::command]
async fn get_app_info() -> Result<serde_json::Value, String> {
    Ok(serde_json::json!({
        "name": "Tavern RE",
        "version": "0.1.0",
        "description": "Modern AI chat client"
    }))
}

#[tauri::command]
async fn create_chat(
    title: String,
    character_id: Option<String>,
    state: State<'_, AppState>
) -> Result<Chat, String> {
    let chat = Chat {
        id: uuid::Uuid::new_v4().to_string(),
        title,
        character_id,
        created_at: chrono::Utc::now().to_rfc3339(),
        updated_at: chrono::Utc::now().to_rfc3339(),
        metadata: None,
    };
    
    let mut chats = state.chats.lock().await;
    chats.insert(chat.id.clone(), chat.clone());
    
    Ok(chat)
}

#[tauri::command]
async fn get_all_chats(state: State<'_, AppState>) -> Result<Vec<Chat>, String> {
    let chats = state.chats.lock().await;
    Ok(chats.values().cloned().collect())
}

#[tauri::command]
async fn create_character(
    character: serde_json::Value,
    state: State<'_, AppState>
) -> Result<Character, String> {
    let mut char_data: Character = serde_json::from_value(character)
        .map_err(|e| format!("Invalid character data: {}", e))?;
    
    char_data.id = uuid::Uuid::new_v4().to_string();
    char_data.created_at = chrono::Utc::now().to_rfc3339();
    char_data.updated_at = chrono::Utc::now().to_rfc3339();
    
    let mut characters = state.characters.lock().await;
    characters.insert(char_data.id.clone(), char_data.clone());
    
    Ok(char_data)
}

#[tauri::command]
async fn get_all_characters(state: State<'_, AppState>) -> Result<Vec<Character>, String> {
    let characters = state.characters.lock().await;
    Ok(characters.values().cloned().collect())
}

#[tauri::command]
async fn get_setting(key: String, state: State<'_, AppState>) -> Result<Option<serde_json::Value>, String> {
    let settings = state.settings.lock().await;
    Ok(settings.get(&key).cloned())
}

#[tauri::command]
async fn set_setting(
    key: String,
    value: serde_json::Value,
    state: State<'_, AppState>
) -> Result<(), String> {
    let mut settings = state.settings.lock().await;
    settings.insert(key, value);
    Ok(())
}

#[tauri::command]
async fn get_all_settings(state: State<'_, AppState>) -> Result<HashMap<String, serde_json::Value>, String> {
    let settings = state.settings.lock().await;
    Ok(settings.clone())
}

// Placeholder for future AI integration
#[tauri::command]
async fn send_message(
    message: String,
    character_id: Option<String>,
    _provider_config: Option<serde_json::Value>
) -> Result<String, String> {
    // This is a placeholder - will be implemented in later tasks
    println!("Received message: {} for character: {:?}", message, character_id);
    Ok(format!("Echo: {}", message))
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! Welcome to Tavern RE!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            let handle = app.handle().clone();
            tauri::async_runtime::block_on(async move {
                let db_pool = database::init_database(&handle)
                    .await
                    .expect("Failed to initialize database");
                let app_state = AppState {
                    db_pool,
                    chats: Default::default(),
                    characters: Default::default(),
                    settings: Default::default(),
                };
                handle.manage(app_state);
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            initialize_app,
            get_app_info,
            create_chat,
            get_all_chats,
            create_character,
            get_all_characters,
            get_setting,
            set_setting,
            get_all_settings,
            send_message
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
