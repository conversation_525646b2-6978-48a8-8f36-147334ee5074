{"$schema": "https://schema.tauri.app/config/2", "productName": "Tavern RE", "version": "0.1.0", "identifier": "com.shi73.tavern-re", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../build"}, "app": {"windows": [{"title": "Tavern RE - AI Chat Client", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "decorations": true, "transparent": false, "alwaysOnTop": false, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "category": "Productivity", "shortDescription": "Modern AI chat client", "longDescription": "A modern AI chat client built with Tauri and SvelteKit, supporting multiple AI providers and character management."}}