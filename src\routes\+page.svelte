<script lang="ts">
  import { onMount } from 'svelte';
  import { appState, loadingState, errorState } from '$lib/stores/app.svelte.js';
  import { chatState } from '$lib/stores/chat.svelte.js';
  import { characterState } from '$lib/stores/character.svelte.js';
  import { chatApi, characterApi } from '$lib/api.js';

  let messageInput = $state('');

  async function sendMessage() {
    if (!messageInput.trim()) return;

    try {
      const response = await chatApi.sendMessage(
        messageInput,
        appState.selectedCharacter || undefined
      );
      
      console.log('AI Response:', response);
      messageInput = '';
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }

  async function createTestCharacter() {
    try {
      const character = await characterApi.createCharacter({
        name: 'Test Assistant',
        description: 'A helpful AI assistant for testing',
        personality: 'Friendly and helpful',
        scenario: 'You are a helpful AI assistant.',
        first_message: 'Hello! How can I help you today?',
        example_dialogs: [
          'User: Hello\nAssistant: Hi there! How can I assist you?'
        ],
        tags: ['assistant', 'helpful']
      });
      
      console.log('Created character:', character);
    } catch (error) {
      console.error('Failed to create character:', error);
    }
  }

  onMount(async () => {
    // Load initial data
    try {
      const characters = await characterApi.getAllCharacters();
      characterState.characters = characters;
      console.log('Loaded characters:', characters);
    } catch (error) {
      console.error('Failed to load characters:', error);
    }
  });
</script>

<svelte:head>
  <title>Tavern RE - AI Chat Client</title>
</svelte:head>

<main class="main-container">
  <header class="header">
    <h1>Tavern RE</h1>
    <p>Modern AI Chat Client</p>
  </header>

  {#if loadingState.isLoading}
    <div class="loading">
      <div class="spinner"></div>
      <p>{loadingState.loadingMessage}</p>
    </div>
  {:else if !appState.isInitialized}
    <div class="error">
      <p>Failed to initialize application</p>
    </div>
  {:else}
    <div class="content">
      <div class="chat-section">
        <h2>Chat</h2>
        
        <div class="message-input">
          <input
            type="text"
            bind:value={messageInput}
            placeholder="Type your message..."
            onkeydown={(e) => e.key === 'Enter' && sendMessage()}
          />
          <button onclick={sendMessage} disabled={!messageInput.trim()}>
            Send
          </button>
        </div>

        {#if chatState.isStreaming}
          <div class="streaming-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <p>AI is typing...</p>
          </div>
        {/if}
      </div>

      <div class="character-section">
        <h2>Characters</h2>
        <button onclick={createTestCharacter}>Create Test Character</button>
        
        <div class="character-list">
          {#each characterState.characters as character}
            <div class="character-card">
              <h3>{character.name}</h3>
              <p>{character.description}</p>
              <div class="character-tags">
                {#each character.tags as tag}
                  <span class="tag">{tag}</span>
                {/each}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  {/if}

  {#if errorState.isShowingError}
    <div class="error-overlay">
      <div class="error-dialog">
        <h3>Error</h3>
        {#each errorState.errors as error}
          <p>{error.message}</p>
        {/each}
        <button onclick={() => errorState.isShowingError = false}>Close</button>
      </div>
    </div>
  {/if}
</main>

<style>
  .main-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .header h1 {
    color: var(--primary-color);
    margin: 0;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
  }

  .chat-section, .character-section {
    background: var(--secondary-color);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
  }

  .message-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .message-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-color);
    color: var(--text-color);
  }

  .message-input button {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .message-input button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .streaming-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
  }

  .typing-dots {
    display: flex;
    gap: 2px;
  }

  .typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
  }

  .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
  .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

  @keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
  }

  .character-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
  }

  .character-card {
    padding: 1rem;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
  }

  .character-card h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
  }

  .character-card p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .character-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .tag {
    background: var(--primary-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
  }

  .error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .error-dialog {
    background: var(--bg-color);
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    max-width: 400px;
    width: 90%;
  }

  .error-dialog h3 {
    margin: 0 0 1rem 0;
    color: #e74c3c;
  }

  .error-dialog button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 1rem;
  }

  @media (max-width: 768px) {
    .content {
      grid-template-columns: 1fr;
    }
  }
</style>